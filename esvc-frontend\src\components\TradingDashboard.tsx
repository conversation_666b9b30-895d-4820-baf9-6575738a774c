import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import QRCode from 'qrcode';
import '../styles/components/TradeChallenge.css';
import DashboardLayout from './DashboardLayout';
import vectorAsset from '../assets/Vector.png';

interface TradeChallengeProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

type ChallengeStep = 'payment' | 'payment-qr' | 'payment-success' | 'link-account' | 'link-modal' | 'connection-success' | 'dashboard';

// Copy SVG Component
const CopyIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
  </svg>
);

const TradeChallenge: React.FC<TradeChallengeProps> = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<ChallengeStep>('payment');
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [countdown, setCountdown] = useState(1799); // 29:59 in seconds
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState<'coinbase' | 'binance'>('coinbase');
  const qrCanvasRef = useRef<HTMLCanvasElement>(null);

  // Countdown timer effect
  useEffect(() => {
    if (currentStep === 'payment-qr' && countdown > 0) {
      const timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [currentStep, countdown]);

  // Generate QR code when payment QR step is reached
  useEffect(() => {
    if (currentStep === 'payment-qr' && qrCanvasRef.current && selectedCurrency) {
      const walletAddress = 'exeFz9gVzWxMqMzrGpyMWnZRpZBuAkKFef';
      QRCode.toCanvas(qrCanvasRef.current, walletAddress, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      }, (error) => {
        if (error) console.error('QR Code generation error:', error);
      });
    }
  }, [currentStep, selectedCurrency]);

  const handleCurrencySelect = (currency: string) => {
    setSelectedCurrency(currency);
    setCurrentStep('payment-qr');
  };

  const handlePaymentComplete = () => {
    setCurrentStep('payment-success');
  };

  const handleProceedToLinkAccount = () => {
    setCurrentStep('link-account');
  };

  const handleGoBack = () => {
    if (currentStep === 'payment-qr') {
      setCurrentStep('payment');
      setSelectedCurrency('');
    } else if (currentStep === 'payment-success') {
      setCurrentStep('payment-qr');
    } else if (currentStep === 'link-account') {
      setCurrentStep('payment-success');
    } else if (currentStep === 'connection-success') {
      setCurrentStep('link-account');
    } else if (currentStep === 'link-modal') {
      setCurrentStep('link-account');
      setShowModal(false);
    }
  };

  const handleConnectBot = (e: React.FormEvent) => {
    e.preventDefault();
    setShowModal(false);
    setCurrentStep('connection-success');
  };

  const handleGoToDashboard = () => {
    setCurrentStep('dashboard');
  };

  const handleOpenModal = (type: 'coinbase' | 'binance') => {
    setModalType(type);
    setShowModal(true);
    setCurrentStep('link-modal');
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setCurrentStep('link-account');
  };

  // Format countdown time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStepStatus = (step: number) => {
    if (currentStep === 'payment' && step === 1) return 'active';
    if (currentStep === 'payment-qr' && step === 1) return 'active';
    if (currentStep === 'payment-success' && step === 1) return 'completed';
    if (currentStep === 'payment-success' && step === 2) return 'active';
    if ((currentStep === 'link-account' || currentStep === 'link-modal') && step === 1) return 'completed';
    if ((currentStep === 'link-account' || currentStep === 'link-modal') && step === 2) return 'active';
    if (currentStep === 'dashboard' && step <= 3) return 'completed';
    return 'inactive';
  };

  const getProgressBarClass = () => {
    if (currentStep === 'payment-success') return 'step-1-completed step-2-active';
    if (currentStep === 'link-account' || currentStep === 'link-modal') return 'step-1-completed step-2-active';
    if (currentStep === 'dashboard') return 'step-2-completed';
    return '';
  };

  return (
    <DashboardLayout className="trade-challenge-container">
      <div className="trade-challenge-content">
        {/* Go Back Button */}
        {(currentStep === 'payment-qr' || currentStep === 'payment-success' || currentStep === 'link-account' || currentStep === 'connection-success') && (
          <button className="go-back-btn" onClick={handleGoBack}>
            <span>←</span> Go Back
          </button>
        )}

        {/* Join ESVC Trade Challenge Section */}
        <div className="join-challenge-section">
          <h1 className="join-challenge-title">Join the ESVC Trade Challenge</h1>

          {/* Progress Steps */}
          <div className={`challenge-steps ${getProgressBarClass()}`}>
            <div className={`step-item ${getStepStatus(1)}`}>
              <div className="step-circle">
                {getStepStatus(1) === 'completed' ? '✓' : '1'}
              </div>
              <span className="step-label">PAY ONE-TIME FEE</span>
            </div>
            <div className={`step-item ${getStepStatus(2)}`}>
              <div className="step-circle">
                {getStepStatus(2) === 'completed' ? '✓' : '2'}
              </div>
              <span className="step-label">LINK YOUR TRADING ACCOUNT</span>
            </div>
            <div className={`step-item ${getStepStatus(3)}`}>
              <div className="step-circle">
                {getStepStatus(3) === 'completed' ? '✓' : '3'}
              </div>
              <span className="step-label">YOUR TRADING DASHBOARD</span>
            </div>
          </div>

          {/* Step Content */}
          {currentStep === 'payment' && (
            <div className="payment-section">
              <h2 className="payment-title">Pay Your $100 Entry Fee</h2>
              <p className="payment-subtitle">
                Join the challenge by paying a one-time subscription fee of $100 (USDC equivalent).
                Choose your preferred currency to proceed.
              </p>

              <div className="payment-form">
                <label className="payment-label">Choose your payment currency</label>
                <select
                  className="payment-select"
                  value={selectedCurrency}
                  onChange={(e) => handleCurrencySelect(e.target.value)}
                >
                  <option value="">Click to select</option>
                  <option value="USDC">USDC</option>
                  <option value="Bitcoin">Bitcoin</option>
                  <option value="Ethereum">Ethereum</option>
                </select>
              </div>
            </div>
          )}

          {currentStep === 'payment-qr' && (
            <div className="payment-qr-section">
              <h2 className="payment-title">Pay Your $100 Entry Fee</h2>
              <p className="payment-subtitle">
                Join the challenge by paying a one-time subscription fee of $100 (USDC equivalent).
                Choose your preferred currency to proceed.
              </p>

              <div className="payment-form">
                <label className="payment-label">Choose your payment currency</label>
                <div className="currency-display">
                  <span className="selected-currency">{selectedCurrency}</span>
                </div>
              </div>

              <div className="qr-payment-container">
                <div className="qr-code-section">
                  <canvas ref={qrCanvasRef} className="qr-canvas" />
                </div>

                <div className="wallet-address-section">
                  <label className="wallet-label">USDC Wallet Address</label>
                  <div className="wallet-address-display">
                    <span className="wallet-address">exeFz9gVzWxMqMzrGpyMWnZRpZBuAkKFef</span>
                    <button
                      className="copy-btn"
                      onClick={() => {
                        navigator.clipboard.writeText('exeFz9gVzWxMqMzrGpyMWnZRpZBuAkKFef');
                      }}
                    >
                      <CopyIcon />
                    </button>
                  </div>
                </div>

                <div className="payment-timer">
                  <span className="timer-display">{formatTime(countdown)}</span>
                </div>

                <div className="payment-info">
                  <p className="info-text">
                    <span className="info-icon">ℹ</span>
                    Do not close this page until deposit is complete.
                  </p>
                </div>

                <button className="deposit-complete-btn" onClick={handlePaymentComplete}>
                  I Have Deposited
                </button>
              </div>
            </div>
          )}

          {currentStep === 'payment-success' && (
            <div className="payment-success-section">
              <div className="success-card">
                <div className="success-icon">
                  <div className="checkmark-circle">
                    <span className="checkmark">✓</span>
                  </div>
                </div>
                <h2 className="success-title">
                  Your ${selectedCurrency === 'USDT' ? '100' : selectedCurrency === 'BTC' ? '0.0015' : '0.04'} payment has been received successfully.
                </h2>
                <p className="success-subtitle">
                  You're now one step closer to unlocking automated trading with the ESVC Trade Bot. Let's connect your preferred exchange to get started.
                </p>
                <button className="proceed-btn" onClick={handleProceedToLinkAccount}>
                  Proceed to Connect Exchange
                </button>
              </div>
            </div>
          )}

          {currentStep === 'link-account' && (
            <div className="link-account-section">
              <h2 className="link-title">Link Your Trading Account</h2>
              <p className="link-subtitle">
                Choose the exchange you'd like to connect and enter your API credentials below. This
                enables the trading bot to trade on your behalf securely.
              </p>

              <div className="api-info">
                <div className="info-icon">ℹ</div>
                <p className="info-text">
                  This is 100% safe as we don't have access to sight or withdrawals on your Binance or Coinbase Account.
                  We only use the trade API.
                </p>
              </div>

              <div className="api-tutorial">
                <button className="tutorial-btn">
                  <span className="tutorial-icon">▶</span>
                  Watch: How to Get Your API Key
                </button>
              </div>

              <div className="exchange-options">
                <button
                  className="exchange-btn coinbase-btn"
                  onClick={() => handleOpenModal('coinbase')}
                >
                  <div className="exchange-icon">C</div>
                  <div className="exchange-info">
                    <span className="exchange-name">Coinbase API</span>
                    <span className="exchange-desc">Trade using Coinbase API</span>
                  </div>
                  <span className="link-text">Link Coinbase</span>
                </button>

                <button
                  className="exchange-btn binance-btn"
                  onClick={() => handleOpenModal('binance')}
                >
                  <div className="exchange-icon">B</div>
                  <div className="exchange-info">
                    <span className="exchange-name">Binance API</span>
                    <span className="exchange-desc">Trade using Binance API</span>
                  </div>
                  <span className="link-text">Link Binance</span>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Modal for API Key Input */}
        {showModal && (
          <div className="modal-overlay">
            <div className="modal-content">
              <div className="modal-header">
                <h3 className="modal-title">
                  Link Your {modalType === 'coinbase' ? 'Coinbase' : 'Binance'}
                </h3>
                <button className="modal-close" onClick={handleCloseModal}>
                  ×
                </button>
              </div>

              <div className="modal-body">
                <p className="modal-subtitle">
                  Copy your API keys from your {modalType === 'coinbase' ? 'Coinbase.com' : 'Binance.com'} account and paste here.
                  You can only copy this from {modalType === 'coinbase' ? 'Coinbase.com' : 'Binance.com'} website and not the {modalType === 'coinbase' ? 'Coinbase' : 'Binance'} App.
                </p>

                <form className="api-form" onSubmit={handleConnectBot}>
                  <div className="form-group">
                    <label className="form-label">API Key</label>
                    <input
                      type="text"
                      className="form-input"
                      placeholder="Enter API Key"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Secret Key</label>
                    <input
                      type="password"
                      className="form-input"
                      placeholder="Enter Secret Key"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Trading Capital ($)</label>
                    <input
                      type="number"
                      className="form-input"
                      placeholder="1000"
                    />
                  </div>

                  <div className="form-info">
                    <div className="info-icon">ℹ</div>
                    <p className="info-text">
                      The amount you want the bot to trade with. Make sure this amount is available in USDT in
                      your linked exchange account.
                    </p>
                  </div>

                  <button type="submit" className="connect-btn">
                    Connect to Bot
                  </button>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Connection Success Step */}
        {currentStep === 'connection-success' && (
          <div className="connection-success-container">
            <div className="connection-success-card">
              <div className="success-icon">
                <div className="checkmark-circle">
                  <span className="checkmark">✓</span>
                </div>
              </div>
              <h2 className="success-title">
                Your trading account is now securely linked to the ESVC Trade Bot.
              </h2>
              <p className="success-subtitle">
                We'll begin monitoring your account and executing trades once capital is detected in your exchange.
                Make sure you've funded your exchange wallet with USDT and a little extra for fees.
              </p>
              <button className="dashboard-btn" onClick={handleGoToDashboard}>
                Go to Trade Bot Dashboard
              </button>
            </div>
          </div>
        )}

        {/* More Than Trading Section */}
        <div className="more-than-trading">
          <div className="more-than-trading-content">
            <h2 className="more-than-trading-title">More Than Trading. Fueling Innovation</h2>
            <p className="more-than-trading-text">
              Your trading journey with us is just the beginning. Stake ESVC to earn daily ROI and unlock the
              chance to pitch your own startup ideas for funding. We reinvest a portion of our platform's profit to
              support bold solutions from our staking community.
            </p>
            <div className="more-than-trading-buttons">
              <button className="start-staking-btn">
                Start Staking Now
                <img src={vectorAsset} alt="Vector" className="button-decoration" />
              </button>
              <button className="get-funded-btn">Get Funded</button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default TradeChallenge;
